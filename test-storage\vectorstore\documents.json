{"documents": [{"id": "doc1", "text": "\n// Authentication module for the application\nclass AuthService {\n    constructor(config) {\n        this.config = config;\n        this.tokenExpiry = config.tokenExpiry || 3600; // Default 1 hour\n    }\n    \n    /**\n     * Authenticate a user with username and password\n     * @param {string} username - The user's username\n     * @param {string} password - The user's password\n     * @returns {Promise<Object>} - Authentication result with token\n     */\n    async authenticate(username, password) {\n        // In a real implementation, this would validate against a database\n        if (username === 'admin' && password === 'password') {\n            return {\n                success: true,\n                token: this.generateToken(),\n                user: { id: 1, username, role: 'admin' }\n            };\n        }\n        \n        return {\n            success: false,\n            message: 'Invalid username or password'\n        };\n    }\n    \n    /**\n     * Generate a JWT token\n     * @private\n     * @returns {string} - JWT token\n     */\n    generateToken() {\n        // In a real implementation, this would use a JWT library\n        return 'sample-jwt-token-' + Math.random().toString(36).substring(2);\n    }\n    \n    /**\n     * Verify a token is valid\n     * @param {string} token - The token to verify\n     * @returns {boolean} - Whether the token is valid\n     */\n    verifyToken(token) {\n        // In a real implementation, this would validate the JWT\n        return token && token.startsWith('sample-jwt-token-');\n    }\n}\n\nmodule.exports = AuthService;\n", "embedding": [0.021957991206756233, 0.7839838653348705, -0.8286856529466289, 0.7102580474381912, 0.13367447079607642, 0.9667541238422688, -0.6009570716950634, -0.050635172833112474, -0.10371727479515958, -0.9600796567912577, 0.3354346428924089, -0.5354898976843381, -0.9804092467704857, 0.6930024337514622, 0.6006379340120684, -0.7996849883707013, -0.010694105281320532, -0.04316088791539263, 0.3160111128059988, 0.8438094240086995, -0.6333273848358951, -0.017502129846226122, -0.15429681592848477, 0.6716987532283314, -0.20416668392237192, -0.26341273036175217, 0.3828245499966081, 0.09146628248444033, 0.5372090325329473, 0.305568434685759, -0.8347076261711237, -0.7854851100057454, -0.9190099202075914, 0.37361115122142063, 0.16523294212527118, -0.18851176168068706, 0.9297707136511475, 0.32536774266027013, -0.5790392353474969, 0.07226421448317843, -0.9882243300217257, 0.11282325424899398, 0.7203672712406903, -0.9783686841802663, -0.7323255569666784, -0.041821395852826626, 0.5900174559906475, 0.7551983162198832, 0.7386159800408585, 0.33002771820939714, 0.08416199539086167, -0.9156071746584353, -0.10457469282220755, 0.9044531691653583, -0.10345102511187321, -0.9803894725415119, 0.8640686560882211, 0.7732222218903653, -0.4573120390124683, -0.6904517411561195, -0.10547755604038711, -0.7081917666614124, 0.51730765874263, 0.6008217740202624, 0.09522177923117292, 0.8168831951889852, 0.6223370238115815, -0.8753339864124392, -0.5067541603948653, -0.6770894289853406, -0.3673104582831508, 0.26005216772145134, -0.4050286434543482, -0.7783458370314302, 0.451922395905334, -0.27918577774722797, 0.5605488143458888, 0.14496470340887857, 0.3294870847700957, 0.38721812628075014, -0.46028819804735654, -0.17135366929703721, 0.7032360928420749, -0.05218654008380286, 0.5193928192946804, -0.6035727173258199, 0.28122838142151485, -0.028889435298042443, 0.3133515041903694, 0.9792638207375344, 0.8719515156674804, -0.47449390447811135, -0.9689908780578813, -0.5566376517675264, 0.821431117236807, 0.221640420824329, -0.6874908530356962, 0.8402002750265001, -0.4328650132651588, -0.5919414543046018, 0.7722496077969843, -0.9104121051872358, 0.06848010553606176, 0.10550285708891627, -0.3059114538298662, 0.8228169046506135, 0.22829203003857934, 0.5621299032112761, -0.48778078392489954, -0.2906861634357685, 0.8821713735246401, 0.034378094856536645, -0.22548458754650724, -0.07273684259713908, 0.011468310738975696, 0.12624440336357168, 0.023647683068336978, -0.5018167077420799, 0.8133083180026932, 0.9599767152646375, 0.8468894945551466, 0.8860847725787986, -0.22962439098679344, 0.7519971017669915, 0.31640032925254635, 0.17375156098534061, -0.6224582064245547, 0.6180165796151624, -0.6470930868540918, 0.40345542611235796, -0.24215006871568479, -0.5886976961021437, -0.19939584669673804, 0.8382562935164182, 0.27988009050960594, -0.4557709571033781, 0.4074111751840488, 0.6836486217178517, -0.7808970215978386, 0.27659640547693165, -0.14511732570636493, 0.9487705747396546, -0.6311419651195176, -0.43663606200503535, -0.9769479279871263, 0.8053332995409312, 0.780905252390558, -0.11655772667566655, -0.2781784995277965, -0.3182920271569123, -0.3677074054416729, -0.7264184610376989, 0.7030481492076364, -0.37151511521530267, -0.9121705268518978, 0.1462363693580695, -0.2322683373370822, 0.5915325239983309, 0.6051217061709266, 0.2540599464227373, -0.12960982080611938, 0.6287453468799895, 0.7165425761113124, -0.7582185070508696, -0.724093461330575, -0.17792860734366567, -0.5695282244675655, 0.03683110567679604, 0.9824434057217974, -0.5006346044925021, 0.878109740377218, -0.2821398783257476, -0.7584002712543176, -0.4678320874796107, 0.1673740890038231, 0.7539353835820091, 0.12315606529544354, -0.5883423425752508, -0.7142759264664877, 0.5365013671678613, -0.13262163175122588, 0.9728730536776, 0.6745460683835738, -0.09872642760308592, 0.3026423488910073, -0.6778986483299496, -0.7323880961367109, 0.2819210732650146, 0.021822933688739443, -0.9035701355564032, 0.10627484603499315, -0.8183420686494327, 0.7949654969597821, -0.17511330786558332, -0.6173291833271759, -0.22234291313792687, -0.32552837846696114, 0.2849801943137269, -0.5221550920784703, 0.22226977826773098, -0.05642369983944695, -0.3288002746489753, 0.20201938315982515, 0.6350248134477003, -0.07615488877102461, 0.6485012658329934, 0.9668250964101435, -0.11472669842514849, -0.4931369326626398, -0.510029540371705, 0.5584313209880611, 0.09500419152236805, 0.7524050875704127, 0.08510897170061282, -0.8940690987327695, 0.027787213116293952, 0.5069106359864217, -0.4091722883280653, -0.5075686683196023, -0.5064355623511618, -0.39177958859077266, 0.12281799065912358, 0.04136998128400604, -0.18722477277738525, 0.5713262896891638, -0.11515168806781828, -0.6545570899446473, -0.3713929531957074, -0.057852502121945815, -0.06617489511710417, 0.23624862836271632, 0.26907212640233436, -0.7280976488246225, -0.10898543643354097, -0.04231067529767163, 0.8765051296505542, -0.06521368304823927, -0.11931539403934455, 0.5058803035571207, 0.896827998287121, -0.9859969133933886, 0.9278254562636534, 0.880882747269792, 0.28728214165156585, 0.14346713418636492, 0.08038085402757877, -0.19025957613001587, -0.6387544238777294, 0.36161563528580754, -0.16276433004927737, 0.4708408053520863, 0.7174363254837974, -0.22177791218363874, -0.4580178287877201, 0.7976498015707847, -0.6588567412252795, -0.5440240120396513, 0.9728680081537795, -0.878310492929852, -0.29035862372059196, 0.25918434971702586, 0.5488772967094784, -0.019084428539029474, -0.08159046037661488, -0.43847291182897896, 0.9527329910681841, -0.1016440867162256, 0.7046159070411475, 0.05878476732913196, 0.3773436378487225, -0.25648560292790146, -0.07374296159716787, 0.9210807944218766, -0.04127479170826209, 0.6064718393131652, -0.6680255854402057, 0.7687103801711759, 0.3305145225788486, -0.6702190915923412, 0.07828260445018209, -0.9675765893578667, 0.6781593197336373, 0.7342867991789408, -0.05386937953068305, -0.25635743534011546, -0.23016875541127657, 0.9632255499477398, 0.5238250147661376, -0.30899730305620343, 0.5397287294469688, -0.6968491713815648, -0.8667092445842317, 0.7925014073943375, -0.21299229491038751, 0.32345203372330156, -0.4513038286186113, -0.5690281759336915, 0.0893371053931249, -0.9604385557269213, 0.2561202484356482, 0.08461527437674032, 0.3371571313901165, 0.454391386222436, -0.18936932943963658, -0.8925552813632214, -0.6193353284773626, 0.0056495049507367945, -0.09206252897430467, -0.04555140561771953, -0.24256958166584264, -0.9009187012321349, -0.28073354037379206, -0.8788868493908706, -0.31798743748971825, -0.24993771984479896, -0.4856659261437932, -0.8591269963670811, -0.1687244831317818, -0.8570777381377339, -0.9906269375955468, 0.951130227039398, -0.33432808659347746, -0.39662626228139386, -0.42506043464357246, 0.6651641889797659, 0.05428245128660114, 0.22525472847223327, 0.11319126594252804, -0.5169295822365485, -0.3572566789316993, 0.22010802080469638, -0.6428241991720975, -0.27740623252527685, -0.8567346598842938, -0.5640839395081092, -0.5990113999829583, -0.10805405051800054, 0.3318211836810532, -0.19329983081919622, 0.26869521975485977, 0.8676689063824665, -0.1962475371177801, -0.9520600887036048, -0.14367650693914147, -0.12813353151613027, -0.648111061135249, 0.4781782623874862, 0.463955204340575, -0.18909469096367948, 0.1685167040878679, -0.7205520108154677, 0.49807740149975643, 0.07631375431486287, -0.9737729529194121, -0.6505533101549155, 0.5914921502984365, 0.7887916602048106, 0.5835468526369936, -0.5625846146298712, -0.6423063720429392, -0.3497998034142418, 0.7803246053543855, 0.469088566326489, 0.714923743537013, 0.03908416864944009, -0.8774952132508669, -0.5832672715416254, -0.6178264524121642, -0.802265617111722, -0.5201890274832577, -0.7521845739198842, -0.4894269969936724, -0.8727683288341086, 0.1551286604089528, -0.41890411642280334, -0.9011710089540452, -0.8295463741870326, -0.8676626593537033, 0.8146321600247286, 0.9601402304258451, -0.056981226353366665, -0.5366638366833945, -0.5619005002889335, 0.8383147493617846], "filePath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projects\\PraxCode\\praxcode\\test-files\\sample.js", "startLine": 1, "endLine": 53, "language": "javascript"}, {"id": "doc2", "text": "\n/**\n * Database service for the application\n */\nexport class DatabaseService {\n    private connection: any;\n    private isConnected: boolean = false;\n    \n    /**\n     * Initialize the database connection\n     * @param config - Database configuration\n     */\n    async initialize(config: DatabaseConfig): Promise<boolean> {\n        try {\n            // In a real implementation, this would connect to a real database\n            this.connection = {\n                query: async (sql: string, params: any[]) => {\n                    console.log('Executing query:', sql, params);\n                    return { rows: [] };\n                },\n                close: async () => {\n                    console.log('Closing connection');\n                    this.isConnected = false;\n                }\n            };\n            \n            this.isConnected = true;\n            return true;\n        } catch (error) {\n            console.error('Failed to connect to database:', error);\n            return false;\n        }\n    }\n    \n    /**\n     * Execute a query on the database\n     * @param sql - SQL query to execute\n     * @param params - Query parameters\n     * @returns Query result\n     */\n    async query(sql: string, params: any[] = []): Promise<any> {\n        if (!this.isConnected) {\n            throw new Error('Database not connected');\n        }\n        \n        return this.connection.query(sql, params);\n    }\n    \n    /**\n     * Close the database connection\n     */\n    async close(): Promise<void> {\n        if (this.isConnected) {\n            await this.connection.close();\n            this.isConnected = false;\n        }\n    }\n}\n\n/**\n * Database configuration interface\n */\nexport interface DatabaseConfig {\n    host: string;\n    port: number;\n    username: string;\n    password: string;\n    database: string;\n}\n", "embedding": [0.19694469389203384, 0.6568080044661775, -0.9153587148081912, -0.038162792236644094, -0.6849159359829637, 0.21395272777300178, 0.6704441075240242, -0.10449046261794193, 0.9457884714896871, 0.5842373980252895, 0.01284711285260487, -0.10688800249079122, -0.6048975573820394, 0.895445989787063, -0.3354035419240242, 0.3215899603857437, 0.3496321001744551, 0.3208319367564232, 0.6497976397992113, 0.21553823507349668, 0.41605877688240733, -0.6055307121340654, -0.10251189222546886, 0.6966306210598914, 0.6469985182461637, -0.6982230717720155, -0.3943668064116266, 0.5977894436861657, 0.8922535061870236, 0.9065570335190123, -0.049447179230261096, -0.35897544056604014, 0.4698478202528471, -0.09662136662722443, -0.7658150619983757, -0.9842819158399236, -0.6994740426984416, 0.10793654028523436, 0.22214513251826018, 0.6124429403392138, 0.8871764275429772, 0.028021749561280007, -0.8282639012776962, 0.33530646238928297, -0.907873223671452, 0.025439294405568802, -0.12025929236454092, 0.27038915574182276, -0.5619867052011549, 0.7596527297893769, 0.6077542499627615, -0.4708520329527306, 0.1764102213969907, -0.4069465112160433, 0.15497477228895518, -0.7468495440251046, -0.34680250271529944, 0.5473121907076672, -0.08246663129700149, -0.8877421048172431, 0.1723377528864054, 0.3887560754804009, -0.28418815478561443, 0.8981490364949938, -0.8499240576445608, -0.870879605106893, 0.781895376420155, 0.2995711143958526, 0.7730518884418576, -0.46972893974163554, 0.9278150423716252, 0.5586334342237782, -0.9603743065908263, 0.8314357185705257, 0.9395902373144276, 0.8349521094632824, 0.2120705933735354, 0.6413148976142717, 0.7413681967545047, 0.9639542024283938, -0.7637288014164159, 0.3154121162511623, 0.07607060658386233, -0.9085898005268067, -0.24743941206411213, -0.20098136584157977, -0.34345118504983807, 0.8371924994533262, 0.4888958946855655, 0.7871963246717559, 0.5226117602510563, -0.5416469308059493, -0.422883345217425, 0.07574438561415775, 0.1431806853391624, 0.3427027913271825, 0.032074628008676775, -0.13875549846853596, -0.5676400983297287, 0.4834425193678027, -0.13206007547701715, 0.9111631273745098, -0.19368529640897814, -0.5734384906956058, -0.6030365495122019, -0.34849349484032643, 0.6584169174751047, 0.8287287775314485, -0.08471505764157161, -0.3362281808588201, -0.7615053063760584, 0.8954660230593223, 0.11154660803370531, -0.1976568452861076, -0.6924233886717128, 0.27933189475334874, 0.09132362826383833, -0.05232207361840757, -0.9973299627491738, 0.2852453865058413, 0.008378779309315298, -0.44660153680266257, -0.08655650618095345, 0.35504522854335496, -0.3070675123752875, 0.829456359074455, -0.38716531087755346, -0.5186506269730446, 0.522835025612848, -0.38083590651470534, 0.4461896910233687, 0.7736234863772551, -0.34242414106129493, 0.2439291850465093, -0.936637219921062, 0.9638565596932636, 0.47813089046755897, -0.20689794057126543, 0.8484414109804694, 0.6902735990270545, 0.190585058787601, -0.1490306436780089, 0.16158017530445923, 0.03875434092927854, -0.6322020894757787, 0.18773751324072085, 0.13600090749937932, 0.17257752965343087, -0.8028034882054156, 0.04397941617890666, 0.3084409794758214, 0.6555583339164279, 0.43304342403425844, -0.8955556360488282, 0.9104998844869501, 0.3784683436568632, 0.04969281205040721, 0.45475948983368, -0.7689094226893083, 0.16705517728287456, 0.28061298570929916, -0.9552955150655404, 0.4399822037767809, 0.00044780774358299524, -0.5989112751230103, 0.3003912169194489, -0.16755676301208, 0.39340627067217726, 0.9814961305514065, 0.527568709563417, -0.07508199636736101, 0.7422238391791849, -0.8642392828977323, -0.7564180332247306, 0.669718231963973, 0.4108949881509085, 0.901538985100939, 0.3200990995563835, 0.22847504041474398, 0.17349449167752784, -0.3305052828736761, -0.32825026802099444, 0.8268847257086014, 0.32039119785788683, 0.25847932859401324, -0.06668818054624825, -0.10497840777905276, 0.1737269348031769, -0.526138104457265, -0.8382399695111835, -0.9939496136890602, -0.5945238698463791, 0.1574230737116662, 0.543833455628262, 0.6924513663114187, -0.7381386191308308, 0.8180308505682747, 0.45255933782212443, 0.7545792328253231, -0.21221226670085303, 0.2963476494725992, 0.35193524901416584, 0.4399000859564186, 0.869140143031057, -0.9309155050187932, 0.1535884032510113, 0.22937298392707461, 0.3072404057291025, 0.5251204904362723, 0.3377928280696554, 0.6513499574092121, -0.2664184220862946, 0.3863402007070822, -0.3502081039130309, -0.9434368858237869, 0.28246472795927025, -0.5781100791843325, 0.6417356133592493, -0.9698698744208762, -0.413528536510988, -0.7205349401935401, 0.5039199966294463, -0.8463242194333427, 0.055797178356038035, -0.8951200005721192, 0.6849652211107582, -0.9044156456504391, -0.7818186874579376, -0.3478242711455084, 0.7392946688567359, -0.7948010940127643, -0.667692677657532, 0.45210851084390713, -0.2902515441565634, 0.6859741702566207, -0.2468283349275766, -0.357589746287748, 0.4981962569051772, -0.15337097493200247, -0.7800531836767801, -0.9924391651926463, -0.3215121533420202, 0.7849504224363111, -0.8886120944503393, 0.17879402344433926, -0.17716790554712158, 0.24396047046434566, -0.40270263504929105, 0.06560644057581566, -0.08140547432019885, -0.04905759889041095, 0.29447762073523887, 0.4432626708501277, -0.2948302558555196, -0.6692555466054171, 0.7711347515083591, 0.8768700980044559, 0.5004646817517293, -0.8200804822045042, -0.6529014153440729, 0.2393805140681491, -0.19536356235144137, 0.6240568680048386, 0.06761625569460561, 0.2020427869650243, -0.8094479952737084, -0.9443770585115137, 0.5430259359835228, 0.3059552663128646, 0.3617004872224867, 0.7795808116677576, 0.6275323907307713, -0.7118789931026694, 0.6944494660133258, -0.1810663744683807, -0.34273984649859246, 0.2753542957838375, -0.5282571804792049, 0.48689130047167284, -0.7690339985986179, -0.0664658949375907, 0.5200578351476204, 0.2950103433612141, -0.688714205710732, 0.38478593840494213, 0.6954262998021519, -0.8639222447169161, -0.5140724271914268, 0.9361314406089751, 0.4328674064370035, 0.570419678205186, -0.7010311259106978, -0.5406077303552248, 0.3804654294903007, 0.6502524253645463, -0.9088257773409114, 0.0144811508145124, -0.8341492123008685, 0.4401534020094804, -0.035340109760848115, -0.7840676675086544, 0.8960665635829246, -0.11209902845739261, 0.5113759659014789, 0.19748214779692885, 0.9767731774667201, -0.4880296832054216, -0.250215318554317, 0.7647233005362088, -0.042569454207175283, -0.6879189642006818, 0.8975526537851564, -0.8440709645935116, 0.6645384320056875, -0.5240058953936662, -0.2644484715847564, 0.6347324139971211, 0.05542445927866346, -0.6773759131998145, 0.055758038014965816, -0.012191560294308346, 0.7098059313444316, 0.04180696788475746, -0.4235343725502756, -0.4805968860895695, -0.5957030724297208, 0.9996975793542706, -0.7914071469372903, 0.5353954152764162, -0.5172543786537602, 0.5906820770698187, -0.9500625280570412, 0.20041974293459663, 0.6711836551915566, 0.8106018710529024, 0.9917511463618132, -0.5756978546270064, 0.2017000479938984, 0.8505962523587351, 0.9815481981003922, -0.49773184858594766, 0.6515010432820181, 0.7340508241726589, -0.8166715857576743, 0.3304913945603567, 0.13715938917053583, 0.31122079127070457, 0.9559018127711734, -0.0005113758235197885, -0.7306180821666102, -0.870667385774321, -0.6017525195521123, 0.93941351239704, 0.9835467852091782, -0.7851595593182141, 0.991392635270119, -0.6217778120627697, 0.772736590723277, 0.014039832447368727, 0.8292943323154751, -0.7906147379861665, -0.8495366837254004, -0.45676987242826117, 0.48084257103218375, 0.03456866406642112, 0.9663769624407972, 0.4523274360522169, -0.8088997955120232, -0.2565937054183389, 0.7918194849349289, -0.8247307128518049, -0.17965898456906348, -0.03619002905256563, -0.7738971573482152, 0.5486740186609111, 0.9491525028996972, -0.23040595858324586, 0.46547272840341725, -0.32721625357508444, 0.34761864130236164, -0.5259905394080842, -0.3005357297558793, -0.951013472508317, -0.5934657046881799], "filePath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projects\\PraxCode\\praxcode\\test-files\\database.ts", "startLine": 1, "endLine": 70, "language": "typescript"}], "metadata": {"embeddingDimension": 384, "created": "2025-05-01T12:13:24.566Z", "version": "1.0.0"}}