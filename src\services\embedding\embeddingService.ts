import axios from 'axios';
import { logger } from '../../utils/logger';
import { ConfigurationManager } from '../../utils/configurationManager';
import { CacheService } from '../cache/cacheService';
import * as crypto from 'crypto';

/**
 * Interface for embedding options
 */
export interface EmbeddingOptions {
    batchSize?: number;
}

/**
 * Embedding Service class
 */
export class EmbeddingService {
    private configManager: ConfigurationManager;
    private cacheService: CacheService | null = null;
    private cacheEnabled: boolean = true;
    private cacheTTL: number = 24 * 60 * 60 * 1000; // 24 hours

    /**
     * Constructor
     * @param configManager The configuration manager
     * @param cacheService Optional cache service
     */
    constructor(configManager: ConfigurationManager, cacheService?: CacheService) {
        this.configManager = configManager;
        this.cacheService = cacheService || null;
    }

    /**
     * Set the cache service
     * @param cacheService The cache service
     */
    setCacheService(cacheService: CacheService): void {
        this.cacheService = cacheService;
    }

    /**
     * Enable or disable caching
     * @param enabled Whether caching is enabled
     */
    setCacheEnabled(enabled: boolean): void {
        this.cacheEnabled = enabled;
    }

    /**
     * Set the cache TTL
     * @param ttl The cache TTL in milliseconds
     */
    setCacheTTL(ttl: number): void {
        this.cacheTTL = ttl;
    }

    /**
     * Generate embeddings for texts (OPTIMIZED for performance)
     * @param texts The texts to generate embeddings for
     * @param options The embedding options
     */
    async generateEmbeddings(texts: string[], options?: EmbeddingOptions): Promise<number[][]> {
        const config = this.configManager.getConfiguration();

        // OPTIMIZATION: Adaptive batch sizing based on text length
        const avgTextLength = texts.reduce((sum, text) => sum + text.length, 0) / texts.length;
        const adaptiveBatchSize = avgTextLength > 1000 ? 5 : (options?.batchSize || 8); // Reduced default batch size

        logger.debug(`Generating embeddings for ${texts.length} texts with adaptive batch size: ${adaptiveBatchSize}`);

        // OPTIMIZATION: Pre-filter empty or very short texts
        const validTexts = texts.filter(text => text.trim().length > 10);
        if (validTexts.length !== texts.length) {
            logger.debug(`Filtered out ${texts.length - validTexts.length} invalid texts`);
        }

        // Process in batches to avoid overloading the API
        const batches: string[][] = [];
        for (let i = 0; i < validTexts.length; i += adaptiveBatchSize) {
            batches.push(validTexts.slice(i, i + adaptiveBatchSize));
        }

        const allEmbeddings: number[][] = [];
        let processedBatches = 0;

        for (const batch of batches) {
            try {
                processedBatches++;
                logger.debug(`Processing embedding batch ${processedBatches}/${batches.length} (${batch.length} texts)`);

                // OPTIMIZATION: Check cache more efficiently
                const cacheResults = await this.getCachedEmbeddingsOptimized(batch, config.embeddingModel);

                if (cacheResults.allCached) {
                    logger.debug(`Using cached embeddings for entire batch (${batch.length} texts)`);
                    allEmbeddings.push(...cacheResults.embeddings);
                    continue;
                }

                // Generate embeddings for uncached texts only
                const textsToGenerate = cacheResults.uncachedTexts;
                if (textsToGenerate.length > 0) {
                    logger.debug(`Generating embeddings for ${textsToGenerate.length} uncached texts`);
                    const newEmbeddings = await this.generateEmbeddingsWithOllama(textsToGenerate, config.embeddingModel);

                    // Merge cached and new embeddings in correct order
                    const mergedEmbeddings = this.mergeEmbeddings(batch, cacheResults.cachedMap, textsToGenerate, newEmbeddings);
                    allEmbeddings.push(...mergedEmbeddings);

                    // Cache the new embeddings
                    await this.cacheEmbeddings(textsToGenerate, newEmbeddings, config.embeddingModel);
                } else {
                    // All were cached
                    allEmbeddings.push(...cacheResults.embeddings);
                }

                // OPTIMIZATION: Add small delay between batches to prevent overwhelming
                if (processedBatches < batches.length) {
                    await new Promise(resolve => setTimeout(resolve, 50));
                }
            } catch (error) {
                logger.error(`Failed to generate embeddings for batch ${processedBatches}`, error);

                // OPTIMIZATION: Use fallback embeddings instead of failing completely
                logger.warn(`Using fallback embeddings for batch ${processedBatches}`);
                const fallbackEmbeddings = batch.map(() => this.generateRandomEmbedding(384));
                allEmbeddings.push(...fallbackEmbeddings);
            }
        }

        // Handle case where some original texts were filtered out
        const finalEmbeddings: number[][] = [];
        let validIndex = 0;

        for (const originalText of texts) {
            if (originalText.trim().length > 10) {
                finalEmbeddings.push(allEmbeddings[validIndex]);
                validIndex++;
            } else {
                // Use fallback for invalid texts
                finalEmbeddings.push(this.generateRandomEmbedding(384));
            }
        }

        logger.debug(`Generated ${finalEmbeddings.length} embeddings total`);
        return finalEmbeddings;
    }

    /**
     * Get cached embeddings for texts
     * @param texts The texts to get embeddings for
     * @param model The model used for embeddings
     */
    private async getCachedEmbeddings(texts: string[], model: string): Promise<number[][]> {
        // If caching is disabled or no cache service, return empty array
        if (!this.cacheEnabled || !this.cacheService) {
            return [];
        }

        const cachedEmbeddings: number[][] = [];

        for (const text of texts) {
            // Create a cache key based on the text and model
            const cacheKey = this.createEmbeddingCacheKey(text, model);

            // Try to get from cache
            const cachedEmbedding = await this.cacheService.get<number[]>(cacheKey);

            if (cachedEmbedding) {
                cachedEmbeddings.push(cachedEmbedding);
            } else {
                // If any embedding is not cached, we need to generate all of them
                // This is because we need to return embeddings in the same order as the texts
                return [];
            }
        }

        return cachedEmbeddings;
    }

    /**
     * Cache embeddings for texts
     * @param texts The texts
     * @param embeddings The embeddings
     * @param model The model used for embeddings
     */
    private async cacheEmbeddings(texts: string[], embeddings: number[][], model: string): Promise<void> {
        // If caching is disabled or no cache service, do nothing
        if (!this.cacheEnabled || !this.cacheService) {
            return;
        }

        // Cache each embedding
        for (let i = 0; i < texts.length; i++) {
            const text = texts[i];
            const embedding = embeddings[i];

            // Create a cache key based on the text and model
            const cacheKey = this.createEmbeddingCacheKey(text, model);

            // Cache the embedding
            await this.cacheService.set(cacheKey, embedding, this.cacheTTL, true);
        }
    }

    /**
     * Create a cache key for an embedding
     * @param text The text
     * @param model The model
     */
    private createEmbeddingCacheKey(text: string, model: string): string {
        // Create a hash of the text to use as the cache key
        const hash = crypto.createHash('sha256').update(text).digest('hex');
        return `embedding:${model}:${hash}`;
    }

    /**
     * Generate embeddings using Ollama
     * @param texts The texts to generate embeddings for
     * @param model The model to use
     */
    private async generateEmbeddingsWithOllama(texts: string[], model: string): Promise<number[][]> {
        const config = this.configManager.getConfiguration();
        const ollamaUrl = config.ollamaUrl;

        const embeddings: number[][] = [];

        // First check if Ollama is running
        try {
            await axios.get(`${ollamaUrl}/api/version`, { timeout: 2000 });
        } catch (error) {
            logger.error('Failed to connect to Ollama server', error);

            // Generate fallback random embeddings for testing purposes
            // This allows indexing to work even without Ollama running
            logger.warn('Using fallback random embeddings for testing');
            return texts.map(() => this.generateRandomEmbedding(384)); // 384 is a common embedding dimension
        }

        for (const text of texts) {
            try {
                const response = await axios.post(`${ollamaUrl}/api/embeddings`, {
                    model,
                    prompt: text
                });

                if (response.data && response.data.embedding) {
                    embeddings.push(response.data.embedding);
                } else {
                    logger.warn('Invalid response from Ollama embeddings API, using fallback embedding');
                    embeddings.push(this.generateRandomEmbedding(384));
                }
            } catch (error) {
                logger.error(`Failed to generate embedding for text: ${text.substring(0, 50)}...`, error);
                // Use a fallback embedding instead of failing the whole process
                embeddings.push(this.generateRandomEmbedding(384));
            }
        }

        return embeddings;
    }

    /**
     * Get cached embeddings with optimization (OPTIMIZATION)
     * @param texts The texts to get embeddings for
     * @param model The model used for embeddings
     */
    private async getCachedEmbeddingsOptimized(texts: string[], model: string): Promise<{
        allCached: boolean;
        embeddings: number[][];
        cachedMap: Map<string, number[]>;
        uncachedTexts: string[];
    }> {
        // If caching is disabled or no cache service, return empty result
        if (!this.cacheEnabled || !this.cacheService) {
            return {
                allCached: false,
                embeddings: [],
                cachedMap: new Map(),
                uncachedTexts: texts
            };
        }

        const cachedMap = new Map<string, number[]>();
        const uncachedTexts: string[] = [];

        // Check cache for each text
        for (const text of texts) {
            const cacheKey = this.createEmbeddingCacheKey(text, model);
            const cachedEmbedding = await this.cacheService.get<number[]>(cacheKey);

            if (cachedEmbedding) {
                cachedMap.set(text, cachedEmbedding);
            } else {
                uncachedTexts.push(text);
            }
        }

        const allCached = uncachedTexts.length === 0;
        const embeddings = allCached ? texts.map(text => cachedMap.get(text)!) : [];

        return {
            allCached,
            embeddings,
            cachedMap,
            uncachedTexts
        };
    }

    /**
     * Merge cached and new embeddings in correct order (OPTIMIZATION)
     * @param originalTexts The original text order
     * @param cachedMap Map of cached embeddings
     * @param newTexts The texts that were newly generated
     * @param newEmbeddings The newly generated embeddings
     */
    private mergeEmbeddings(
        originalTexts: string[],
        cachedMap: Map<string, number[]>,
        newTexts: string[],
        newEmbeddings: number[][]
    ): number[][] {
        const result: number[][] = [];
        let newIndex = 0;

        for (const text of originalTexts) {
            const cached = cachedMap.get(text);
            if (cached) {
                result.push(cached);
            } else {
                // Find this text in newTexts and use corresponding embedding
                const textIndex = newTexts.indexOf(text);
                if (textIndex !== -1) {
                    result.push(newEmbeddings[textIndex]);
                } else {
                    // Fallback - should not happen
                    result.push(this.generateRandomEmbedding(384));
                }
            }
        }

        return result;
    }

    /**
     * Generate a random embedding for testing purposes
     * @param dimension The dimension of the embedding
     */
    generateRandomEmbedding(dimension: number): number[] {
        const embedding: number[] = [];
        for (let i = 0; i < dimension; i++) {
            embedding.push(Math.random() * 2 - 1); // Random value between -1 and 1
        }
        return embedding;
    }
}
