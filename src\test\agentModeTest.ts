/**
 * Test file for Agent Mode functionality
 * Tests the enhanced logging and improved prompts for Tasks 2.1-2.4
 */

import * as assert from 'assert';
import * as sinon from 'sinon';
import * as vscode from 'vscode';
import { ActionableItems } from '../ui/components/actionableItems';
import { LLMResponseParser } from '../services/action/llmResponseParser';
import { ActionExecutionService } from '../services/action/actionExecutionService';
import { logger } from '../utils/logger';

suite('Agent Mode Enhanced Logging Tests', () => {
    let loggerSpy: sinon.SinonSpy;
    
    setup(() => {
        // Spy on logger methods to verify logging
        loggerSpy = sinon.spy(logger, 'info');
    });
    
    teardown(() => {
        // Restore spies
        sinon.restore();
    });

    test('ActionableItems should log processing with unique ID', () => {
        const mockWebview = {
            postMessage: sinon.stub()
        } as any;

        const testContent = `
Here's a code change for you:

\`\`\`javascript
function test() {
    console.log("Hello World");
}
\`\`\`

And a command to run:

\`\`\`bash
npm install axios
\`\`\`
        `;

        ActionableItems.processMessage(testContent, mockWebview);

        // Verify that logging includes processing ID
        const logCalls = loggerSpy.getCalls();
        const processingCall = logCalls.find(call => 
            call.args[0].includes('Processing message for actionable items')
        );
        
        assert.ok(processingCall, 'Should log processing message with ID');
        assert.ok(processingCall.args[0].includes('[action_'), 'Should include action ID in log');
    });

    test('LLMResponseParser should parse code changes correctly', () => {
        const testContent = `
I'll help you add a new function:

\`\`\`diff
--- a/src/utils/helpers.js
+++ b/src/utils/helpers.js
@@ -10,6 +10,12 @@
   return result;
 }
 
+export function newHelper(input) {
+  // New functionality
+  return processInput(input);
+}
+
 export default {
   existingHelper,
+  newHelper
 };
\`\`\`
        `;

        const codeChanges = LLMResponseParser.parseCodeChanges(testContent);
        
        assert.strictEqual(codeChanges.length, 1, 'Should parse one code change');
        assert.ok(codeChanges[0].filePath, 'Should extract file path');
        assert.strictEqual(codeChanges[0].filePath, 'src/utils/helpers.js', 'Should extract correct file path');
        assert.ok(codeChanges[0].originalCode, 'Should have original code');
        assert.ok(codeChanges[0].newCode, 'Should have new code');
    });

    test('LLMResponseParser should parse terminal commands correctly', () => {
        const testContent = `
Let's install the dependencies:

\`\`\`bash
npm install axios express
\`\`\`

Then start the development server:

\`\`\`bash
npm run dev
\`\`\`
        `;

        const commands = LLMResponseParser.parseCommands(testContent);
        
        assert.strictEqual(commands.length, 2, 'Should parse two commands');
        assert.strictEqual(commands[0].command, 'npm install axios express', 'Should parse first command correctly');
        assert.strictEqual(commands[1].command, 'npm run dev', 'Should parse second command correctly');
    });

    test('ActionExecutionService should categorize commands correctly', () => {
        // Test command categorization (accessing private method through any)
        const service = ActionExecutionService as any;
        
        assert.strictEqual(service.categorizeCommand('npm install axios'), 'package-manager');
        assert.strictEqual(service.categorizeCommand('git add .'), 'version-control');
        assert.strictEqual(service.categorizeCommand('node app.js'), 'runtime');
        assert.strictEqual(service.categorizeCommand('docker build .'), 'container');
        assert.strictEqual(service.categorizeCommand('ls -la'), 'file-system');
        assert.strictEqual(service.categorizeCommand('echo hello'), 'text-processing');
        assert.strictEqual(service.categorizeCommand('cargo build'), 'rust');
        assert.strictEqual(service.categorizeCommand('dotnet run'), 'dotnet');
        assert.strictEqual(service.categorizeCommand('mvn clean install'), 'java-build');
        assert.strictEqual(service.categorizeCommand('make all'), 'build-tool');
        assert.strictEqual(service.categorizeCommand('unknown command'), 'other');
    });

    test('ActionExecutionService should detect dangerous commands', () => {
        const service = ActionExecutionService as any;
        
        const dangerousPatterns = service.getDangerousPatterns('rm -rf /');
        assert.ok(dangerousPatterns.length > 0, 'Should detect dangerous rm -rf command');
        assert.ok(dangerousPatterns.includes('Recursive force remove'), 'Should identify as recursive force remove');
        
        const safePatterns = service.getDangerousPatterns('npm install axios');
        assert.strictEqual(safePatterns.length, 0, 'Should not flag safe commands as dangerous');
    });

    test('ActionableItems should validate shell commands', () => {
        const items = ActionableItems as any;
        
        assert.ok(items.isValidShellCommand('npm install axios'), 'Should validate npm commands');
        assert.ok(items.isValidShellCommand('git add .'), 'Should validate git commands');
        assert.ok(items.isValidShellCommand('ls'), 'Should validate simple commands');
        assert.ok(!items.isValidShellCommand(''), 'Should reject empty commands');
        assert.ok(!items.isValidShellCommand('   '), 'Should reject whitespace-only commands');
        assert.ok(!items.isValidShellCommand(null as any), 'Should reject null commands');
    });
});

suite('Agent Mode Prompt Integration Tests', () => {
    test('Code change prompts should be imported correctly', () => {
        // Test that the new prompt modules can be imported
        const { codeChangeSystemMessage } = require('../prompts/codeChangePrompt');
        const { terminalCommandSystemMessage } = require('../prompts/terminalCommandPrompt');

        assert.ok(codeChangeSystemMessage, 'Should import code change system message');
        assert.ok(terminalCommandSystemMessage, 'Should import terminal command system message');

        assert.ok(codeChangeSystemMessage.includes('diff format'), 'Should include diff format instructions');
        assert.ok(terminalCommandSystemMessage.includes('bash'), 'Should include bash command instructions');
    });
});
