/**
 * Integration tests for Agent Mode functionality
 * Tests the complete workflow from LLM response to action execution
 */

import * as assert from 'assert';
import * as sinon from 'sinon';
import * as vscode from 'vscode';
import { ActionableItems } from '../ui/components/actionableItems';
import { ActionExecutionService } from '../services/action/actionExecutionService';
import { logger } from '../utils/logger';

suite('Agent Mode Integration Tests', () => {
    let sandbox: sinon.SinonSandbox;
    
    setup(() => {
        sandbox = sinon.createSandbox();
    });
    
    teardown(() => {
        sandbox.restore();
    });

    test('Complete workflow: Parse LLM response with code changes and execute', async () => {
        // Mock VS Code APIs
        const mockWorkspaceEdit = new vscode.WorkspaceEdit();
        const mockApplyEdit = sandbox.stub(vscode.workspace, 'applyEdit').resolves(true);
        const mockShowInformationMessage = sandbox.stub(vscode.window, 'showInformationMessage');
        const mockShowQuickPick = sandbox.stub(vscode.window, 'showQuickPick').resolves({ label: 'Yes' } as vscode.QuickPickItem);
        
        // Mock webview
        const mockWebview = {
            postMessage: sandbox.stub()
        } as any;

        // Simulate LLM response with code changes
        const llmResponse = `
I'll help you add a new utility function to your project:

\`\`\`diff
--- a/src/utils/helpers.js
+++ b/src/utils/helpers.js
@@ -1,5 +1,11 @@
 export function existingHelper() {
   return "existing";
 }
 
+export function formatDate(date) {
+  return date.toISOString().split('T')[0];
+}
+
 export default {
-  existingHelper
+  existingHelper,
+  formatDate
 };
\`\`\`

This adds a new \`formatDate\` function that formats dates as YYYY-MM-DD strings.
        `;

        // Process the LLM response
        ActionableItems.processMessage(llmResponse, mockWebview);

        // Verify that actionable items were sent to webview
        assert.ok(mockWebview.postMessage.called, 'Should send actionable items to webview');
        
        const postMessageCall = mockWebview.postMessage.getCall(0);
        assert.strictEqual(postMessageCall.args[0].command, 'setActionableItems', 'Should send setActionableItems command');
        
        const items = postMessageCall.args[0].items;
        assert.ok(items.codeChanges, 'Should include code changes');
        assert.strictEqual(items.codeChanges.length, 1, 'Should have one code change');
        
        const codeChange = items.codeChanges[0];
        assert.strictEqual(codeChange.filePath, 'src/utils/helpers.js', 'Should extract correct file path');
        assert.ok(codeChange.originalCode, 'Should have original code');
        assert.ok(codeChange.newCode, 'Should have new code');
    });

    test('Complete workflow: Parse LLM response with terminal commands and execute', async () => {
        // Mock VS Code APIs
        const mockTerminal = {
            sendText: sandbox.stub(),
            show: sandbox.stub(),
            dispose: sandbox.stub()
        };
        const mockCreateTerminal = sandbox.stub(vscode.window, 'createTerminal').returns(mockTerminal as any);
        const mockShowQuickPick = sandbox.stub(vscode.window, 'showQuickPick').resolves({ label: 'Yes' } as vscode.QuickPickItem);
        
        // Mock webview
        const mockWebview = {
            postMessage: sandbox.stub()
        } as any;

        // Simulate LLM response with terminal commands
        const llmResponse = `
Let's install the required dependencies for your project:

\`\`\`bash
npm install axios express cors
\`\`\`

Then let's run the tests to make sure everything works:

\`\`\`bash
npm test
\`\`\`
        `;

        // Process the LLM response
        ActionableItems.processMessage(llmResponse, mockWebview);

        // Verify that actionable items were sent to webview
        assert.ok(mockWebview.postMessage.called, 'Should send actionable items to webview');
        
        const postMessageCall = mockWebview.postMessage.getCall(0);
        const items = postMessageCall.args[0].items;
        assert.ok(items.commands, 'Should include commands');
        assert.strictEqual(items.commands.length, 2, 'Should have two commands');
        
        assert.strictEqual(items.commands[0].command, 'npm install axios express cors', 'Should parse first command correctly');
        assert.strictEqual(items.commands[1].command, 'npm test', 'Should parse second command correctly');

        // Test command execution
        const success = await ActionExecutionService.runTerminalCommand('npm install axios express cors');
        
        // Should request confirmation and execute if confirmed
        assert.ok(mockShowQuickPick.called, 'Should request user confirmation');
        assert.ok(mockCreateTerminal.called, 'Should create terminal');
        assert.ok(mockTerminal.sendText.called, 'Should send command to terminal');
        assert.strictEqual(success, true, 'Should return success');
    });

    test('Error handling: Invalid code changes should be filtered out', () => {
        const mockWebview = {
            postMessage: sandbox.stub()
        } as any;

        // Simulate LLM response with invalid code changes (missing file path)
        const llmResponse = `
Here's some code:

\`\`\`javascript
function test() {
    console.log("Hello");
}
\`\`\`

This code doesn't have a file path specified.
        `;

        // Process the LLM response
        ActionableItems.processMessage(llmResponse, mockWebview);

        // Should not send any actionable items since the code change is invalid
        if (mockWebview.postMessage.called) {
            const postMessageCall = mockWebview.postMessage.getCall(0);
            const items = postMessageCall.args[0].items;
            assert.strictEqual(items.codeChanges.length, 0, 'Should filter out invalid code changes');
        }
    });

    test('Safety: Dangerous commands should require additional confirmation', async () => {
        const mockShowQuickPick = sandbox.stub(vscode.window, 'showQuickPick')
            .onFirstCall().resolves({ label: 'Yes' } as vscode.QuickPickItem)  // First confirmation for dangerous command
            .onSecondCall().resolves({ label: 'Yes' } as vscode.QuickPickItem); // Second confirmation for execution
        
        const mockTerminal = {
            sendText: sandbox.stub(),
            show: sandbox.stub(),
            dispose: sandbox.stub()
        };
        const mockCreateTerminal = sandbox.stub(vscode.window, 'createTerminal').returns(mockTerminal as any);

        // Test dangerous command
        const success = await ActionExecutionService.runTerminalCommand('rm -rf node_modules');
        
        // Should request confirmation twice for dangerous commands
        assert.strictEqual(mockShowQuickPick.callCount, 2, 'Should request confirmation twice for dangerous commands');
        assert.ok(success, 'Should execute if user confirms');
    });

    test('Logging: All actions should be logged with unique IDs', () => {
        const loggerSpy = sandbox.spy(logger, 'info');
        
        const mockWebview = {
            postMessage: sandbox.stub()
        } as any;

        const llmResponse = `
\`\`\`diff
--- a/test.js
+++ b/test.js
@@ -1,3 +1,3 @@
-console.log("old");
+console.log("new");
\`\`\`

\`\`\`bash
echo "test"
\`\`\`
        `;

        ActionableItems.processMessage(llmResponse, mockWebview);

        // Verify logging with unique IDs
        const logCalls = loggerSpy.getCalls();
        const actionLogs = logCalls.filter(call => 
            call.args[0].includes('[action_') && call.args[0].includes(']')
        );
        
        assert.ok(actionLogs.length > 0, 'Should log actions with unique IDs');
        
        // Verify different types of logs are present
        const processingLog = logCalls.find(call => 
            call.args[0].includes('Processing message for actionable items')
        );
        const codeAnalysisLog = logCalls.find(call => 
            call.args[0].includes('Code Change Analysis')
        );
        const commandAnalysisLog = logCalls.find(call => 
            call.args[0].includes('Command Analysis')
        );
        
        assert.ok(processingLog, 'Should log processing start');
        assert.ok(codeAnalysisLog, 'Should log code change analysis');
        assert.ok(commandAnalysisLog, 'Should log command analysis');
    });
});
